import axios from 'axios';
import ioredis from 'ioredis';

import { UserType } from '../../types/enums';
import { SubscriptionServerError } from '../../types/errors';
import { ThreeDs } from '../../types/graphql';
import { OfferType } from '../../types/services';
import { contextMock, dpaasHeadersMock } from '../../utils/mock';
import {
  createWalletSubscription,
  constructThreeDSObject,
} from './createWalletSubscription';

jest.mock('axios');
jest.mock('ioredis');

const mockedAxios = axios as jest.Mocked<typeof axios>;

const mockGetUserInfo = jest.fn();
const mockGetUserSubsPlan = jest.fn();
const mockValidateOffer = jest.fn();
const mockCreateMembershipInternal = jest.fn();
const mockGetWalletSubscription = jest.fn();
const mockGetActiveMembership = jest.fn();
const mockSetDefaultPaymentMethodWallet = jest.fn();

jest.mock('../../env', () => ({
  env: {
    BP_PAY_SUBS_ENDPOINT: 'https://mocked.subscriptions.com',
  },
}));
jest.mock('@bp/pulse-common', () => ({
  getDPaaSAuthToken: jest.fn().mockResolvedValue('fake-token'),
  TokenType: jest.fn(),
}));

jest.mock('../../utils/retry', () => ({
  requestWithRetry: (fn: any, ...args: any[]) => fn(...args),
}));

jest.mock('../user/getUserInfo', () => ({
  getUserInfo: (...args: any[]) => mockGetUserInfo(...args),
}));

jest.mock('../user/getUserSubsPlan', () => ({
  getUserSubsPlan: (...args: any[]) => mockGetUserSubsPlan(...args),
}));

jest.mock('../user/createMembershipInternal', () => ({
  createMembershipInternal: (...args: any[]) =>
    mockCreateMembershipInternal(...args),
}));

jest.mock('../offer/validateOffer', () => ({
  validateOffer: (...args: any[]) => mockValidateOffer(...args),
}));

jest.mock('../getWalletSubscription/getWalletSubscription', () => ({
  getWalletSubscription: (...args: any[]) => mockGetWalletSubscription(...args),
}));

jest.mock('../getActiveMembership/getActiveMembership', () => ({
  getActiveMembership: (...args: any[]) => mockGetActiveMembership(...args),
}));

jest.mock(
  '../checkSetDefaultPaymentMethod/checkSetDefaultPaymentMethod',
  () => ({
    checkSetDefaultPaymentMethod: (...args: any[]) =>
      mockSetDefaultPaymentMethodWallet(...args),
  }),
);

const ctx = contextMock();
const userId = 'kate';
const paymentMethodId = 'b271a813-4d3d-41ef-a797-3938d8c1468b';
const offerCode = 'offer-code';
const planId = 'ea94f3a1-df39-40e9-8215-2c2b41962516';

const headers = dpaasHeadersMock();

const mockThreeDS: ThreeDs = {
  eciFlag: '05',
  enrolled: 'Y',
  cavv: 'AAABBIIFmAAAAAAAAAAAAAAAAAA=',
  threeDSServerTransactionId: '12345678-1234-1234-1234-123456789012',
  paresStatus: 'Y',
  threeDSVersion: '2.1.0',
  statusReason: '01',
  dsTransactionId: '87654321-4321-4321-4321-210987654321',
  acsTransactionId: 'abcdef12-3456-7890-abcd-ef1234567890',
};

describe('createWalletSubscription', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  afterAll(async () => {
    // Assuming you have a Redis client instance
    await ioredis.prototype.quit();
  });

  it('should return processing status immediately for PAYG_Wallet user type, paymentMethodId defined and offer code not defined', async () => {
    mockGetUserInfo.mockResolvedValue({
      country: 'UK',
      type: UserType.PAYG_WALLET,
      status: '200',
      message: 'Success',
      partnerType: 'Uber',
      subsEnabled: true,
      membership: [],
    });
    mockGetUserSubsPlan.mockResolvedValue({
      status: '200',
      plans: [{ externalPlanId: planId, planDuration: 30 }],
      message: 'Success',
    });

    const result = await createWalletSubscription(
      ctx,
      userId,
      paymentMethodId,
      undefined,
      headers,
    );

    expect(result).toEqual({
      status: '200',
    });
    expect(mockedAxios.post).toHaveBeenCalled();
  });

  it('should return processing status immediately for PAYG_Wallet user type, paymentMethodId and offerCode defined', async () => {
    mockGetUserInfo.mockResolvedValue({
      country: 'UK',
      type: UserType.PAYG_WALLET,
      status: '200',
      message: 'Success',
      partnerType: 'Uber',
      subsEnabled: true,
      membership: [],
    });
    mockGetUserSubsPlan.mockResolvedValue({
      status: '200',
      plans: [{ externalPlanId: planId, planDuration: 30 }],
      message: 'Success',
    });
    mockValidateOffer.mockResolvedValue({
      offer: { subsPlanId: '1235' },
      isValid: true,
    });

    const result = await createWalletSubscription(
      ctx,
      userId,
      paymentMethodId,
      offerCode,
      headers,
    );

    expect(result).toEqual({
      status: '200',
    });
    expect(mockedAxios.post).toHaveBeenCalled();
  });

  it('should throw an error when offerType is SUBS and subsPlanId is undefined', async () => {
    mockGetUserInfo.mockResolvedValue({
      country: 'UK',
      type: UserType.PAYG_WALLET,
      status: '200',
      message: 'Success',
      partnerType: 'Uber',
      subsEnabled: true,
      membership: [],
    });

    mockGetUserSubsPlan.mockResolvedValue({
      status: '200',
      plans: [{ externalPlanId: planId, planDuration: 30 }],
      message: 'Success',
    });

    mockValidateOffer.mockResolvedValue({
      offer: { offerType: OfferType.SUBS, subsPlanId: undefined },
      isValid: true,
    });

    await expect(
      createWalletSubscription(
        ctx,
        userId,
        paymentMethodId,
        offerCode,
        headers,
      ),
    ).rejects.toThrow(SubscriptionServerError);
  });

  it('should throw an error when offerType is COMBO and subsPlanId is undefined', async () => {
    mockGetUserInfo.mockResolvedValue({
      country: 'UK',
      type: UserType.PAYG_WALLET,
      status: '200',
      message: 'Success',
      partnerType: 'Uber',
      subsEnabled: true,
      membership: [],
    });

    mockGetUserSubsPlan.mockResolvedValue({
      status: '200',
      plans: [{ externalPlanId: planId, planDuration: 30 }],
      message: 'Success',
    });

    mockValidateOffer.mockResolvedValue({
      offer: { offerType: OfferType.COMBO, subsPlanId: undefined },
      isValid: true,
    });

    await expect(
      createWalletSubscription(
        ctx,
        userId,
        paymentMethodId,
        offerCode,
        headers,
      ),
    ).rejects.toThrow(SubscriptionServerError);
  });

  it('should successfully create a subscription when offerType is CREDIT and subsPlanId is undefined', async () => {
    mockGetUserInfo.mockResolvedValue({
      country: 'UK',
      type: UserType.PAYG_WALLET,
      status: '200',
      message: 'Success',
      partnerType: 'Uber',
      subsEnabled: true,
      membership: [],
    });

    mockGetUserSubsPlan.mockResolvedValue({
      status: '200',
      plans: [{ externalPlanId: planId, planDuration: 30 }],
      message: 'Success',
    });

    mockValidateOffer.mockResolvedValue({
      offer: { offerType: OfferType.CREDIT, subsPlanId: undefined },
      isValid: true,
    });

    await expect(
      createWalletSubscription(
        ctx,
        userId,
        paymentMethodId,
        offerCode,
        headers,
      ),
    ).resolves.toStrictEqual({ status: '200' });
  });

  it('should throw error when userType is not PAYG-WALLET', async () => {
    mockGetUserInfo.mockResolvedValue({
      country: 'UK',
      type: UserType.SUBS_WALLET,
      status: '200',
      message: 'Success',
      partnerType: 'Uber',
      subsEnabled: true,
      membership: [],
    });

    await expect(
      createWalletSubscription(
        ctx,
        userId,
        paymentMethodId,
        offerCode,
        headers,
      ),
    ).rejects.toThrow(SubscriptionServerError);
  });

  it('should throw error when userSubsPlan is undefined', async () => {
    mockGetUserInfo.mockResolvedValue({
      country: 'UK',
      type: UserType.PAYG_WALLET,
      status: '200',
      message: 'Success',
      partnerType: 'Uber',
      subsEnabled: true,
      membership: [],
    });

    mockGetUserSubsPlan.mockResolvedValue({});

    await expect(
      createWalletSubscription(
        ctx,
        userId,
        paymentMethodId,
        offerCode,
        headers,
      ),
    ).rejects.toThrow(SubscriptionServerError);
  });

  it('should throw error if subsEnabled is false', async () => {
    mockGetUserInfo.mockResolvedValue({
      country: 'UK',
      type: UserType.PAYG_WALLET,
      subsEnabled: false,
      partnerType: 'Uber',
      status: '401',
      membership: [],
    });

    await expect(
      createWalletSubscription(
        ctx,
        userId,
        paymentMethodId,
        offerCode,
        headers,
      ),
    ).rejects.toThrow(SubscriptionServerError);
  });

  it('should log error if axios.post fails', async () => {
    ctx.logger.error = jest.fn();
    mockGetUserInfo.mockResolvedValue({
      country: 'UK',
      type: UserType.PAYG_WALLET,
      status: '200',
      partnerType: 'Uber',
      message: 'Success',
      subsEnabled: true,
      membership: [],
    });
    mockGetUserSubsPlan.mockResolvedValue({
      status: '200',
      plans: [{ externalPlanId: planId, planDuration: 30 }],
      message: 'Success',
    });

    mockedAxios.post.mockRejectedValue(new Error('Network error'));

    await createWalletSubscription(
      ctx,
      userId,
      paymentMethodId,
      undefined,
      headers,
    );

    await new Promise((resolve) => setImmediate(resolve));

    expect(ctx.logger.error).toHaveBeenCalled();
  });

  it('should throw error when paymentMethodId is undefined', async () => {
    await expect(
      createWalletSubscription(
        ctx,
        userId,
        undefined as any,
        offerCode,
        headers,
      ),
    ).rejects.toThrow(SubscriptionServerError);
  });

  it('should throw error when getUserInfo returns 500 status', async () => {
    mockGetUserInfo.mockResolvedValue({
      status: '500',
    });

    await expect(
      createWalletSubscription(
        ctx,
        userId,
        paymentMethodId,
        offerCode,
        headers,
      ),
    ).rejects.toThrow(SubscriptionServerError);
  });

  it('should throw error when validateOffer fails', async () => {
    mockGetUserInfo.mockResolvedValue({
      country: 'UK',
      type: UserType.PAYG_WALLET,
      status: '200',
      message: 'Success',
      subsEnabled: true,
      membership: [],
    });
    mockValidateOffer.mockResolvedValue({
      status: '500',
    });

    await expect(
      createWalletSubscription(
        ctx,
        userId,
        paymentMethodId,
        offerCode,
        headers,
      ),
    ).rejects.toThrow(SubscriptionServerError);
  });
  it('should throw error if partnerType is ADAC', async () => {
    mockGetUserInfo.mockResolvedValue({
      country: 'UK',
      type: UserType.PAYG_WALLET,
      status: '200',
      message: 'Success',
      partnerType: 'ADAC',
      subsEnabled: true,
      membership: [],
    });

    await expect(
      createWalletSubscription(
        ctx,
        userId,
        paymentMethodId,
        offerCode,
        headers,
      ),
    ).rejects.toThrow(SubscriptionServerError);
  });

  it('should handle createSubscriptionAsync function', async () => {
    ctx.logger.error = jest.fn();
    ctx.logger.info = jest.fn();
    mockGetUserInfo.mockResolvedValue({
      country: 'UK',
      type: UserType.PAYG_WALLET,
      status: '200',
      message: 'Success',
      subsEnabled: true,
      membership: [],
    });
    mockGetUserSubsPlan.mockResolvedValue({
      status: '200',
      plans: [{ externalPlanId: planId, planDuration: 30 }],
      message: 'Success',
    });

    mockValidateOffer.mockResolvedValue({
      offer: { subsPlanId: '1235' },
      isValid: true,
    });

    mockedAxios.post.mockResolvedValue({ data: { subscriptionId: 'mock-id' } });
    mockGetWalletSubscription.mockResolvedValue({
      id: 'mock-id',
      nextBillingDate: '2023-07-01',
    });

    await createWalletSubscription(
      ctx,
      userId,
      paymentMethodId,
      offerCode,
      headers,
    );

    await new Promise((resolve) => setImmediate(resolve));

    expect(mockedAxios.post).toHaveBeenCalled();
    expect(mockGetWalletSubscription).toHaveBeenCalled();
    expect(mockSetDefaultPaymentMethodWallet).toHaveBeenCalled();
    expect(mockCreateMembershipInternal).toHaveBeenCalled();
    expect(ctx.logger.info).toHaveBeenCalledWith(
      expect.stringContaining('Subscription created successfully'),
    );
  });

  it('should handle error in createSubscriptionAsync function', async () => {
    ctx.logger.error = jest.fn();
    mockGetUserInfo.mockResolvedValue({
      country: 'UK',
      type: UserType.PAYG_WALLET,
      status: '200',
      message: 'Success',
      subsEnabled: true,
      membership: [],
    });
    mockGetUserSubsPlan.mockResolvedValue({
      status: '200',
      plans: [{ externalPlanId: planId, planDuration: 30 }],
      message: 'Success',
    });
    const error = new Error('Network error');
    mockedAxios.post.mockRejectedValue(error);

    await createWalletSubscription(
      ctx,
      userId,
      paymentMethodId,
      offerCode,
      headers,
    );

    await new Promise((resolve) => setImmediate(resolve));

    expect(mockedAxios.post).toHaveBeenCalled();
    expect(ctx.logger.error).toHaveBeenCalledWith(
      expect.stringContaining(
        'createWalletSubscriptionError - Error updating user tag scheme',
      ),
      error,
    );
  });

  describe('threeDS parameter tests', () => {
    it('should include threeDS data in request body when threeDS parameter is provided', async () => {
      mockGetUserInfo.mockResolvedValue({
        country: 'UK',
        type: UserType.PAYG_WALLET,
        status: '200',
        message: 'Success',
        partnerType: 'Uber',
        subsEnabled: true,
        membership: [],
      });
      mockGetUserSubsPlan.mockResolvedValue({
        status: '200',
        plans: [{ externalPlanId: planId, planDuration: 30 }],
        message: 'Success',
      });

      const result = await createWalletSubscription(
        ctx,
        userId,
        paymentMethodId,
        undefined,
        headers,
        mockThreeDS,
      );

      expect(result).toEqual({
        status: '200',
      });
      expect(mockedAxios.post).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          customerId: userId,
          paymentMethodId,
          threeDS: mockThreeDS,
        }),
        expect.any(Object),
      );
    });

    it('should not include threeDS data in request body when threeDS parameter is undefined', async () => {
      mockGetUserInfo.mockResolvedValue({
        country: 'UK',
        type: UserType.PAYG_WALLET,
        status: '200',
        message: 'Success',
        partnerType: 'Uber',
        subsEnabled: true,
        membership: [],
      });
      mockGetUserSubsPlan.mockResolvedValue({
        status: '200',
        plans: [{ externalPlanId: planId, planDuration: 30 }],
        message: 'Success',
      });

      const result = await createWalletSubscription(
        ctx,
        userId,
        paymentMethodId,
        undefined,
        headers,
        undefined,
      );

      expect(result).toEqual({
        status: '200',
      });
      expect(mockedAxios.post).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          customerId: userId,
          paymentMethodId,
        }),
        expect.any(Object),
      );

      const callArgs = mockedAxios.post.mock.calls[0];
      const requestBody = callArgs[1];
      expect(requestBody).not.toHaveProperty('eciFlag');
      expect(requestBody).not.toHaveProperty('enrolled');
      expect(requestBody).not.toHaveProperty('cavv');
      expect(requestBody).not.toHaveProperty('threeDSServerTransactionId');
      expect(requestBody).not.toHaveProperty('paresStatus');
      expect(requestBody).not.toHaveProperty('threeDSVersion');
      expect(requestBody).not.toHaveProperty('statusReason');
      expect(requestBody).not.toHaveProperty('dsTransactionId');
      expect(requestBody).not.toHaveProperty('acsTransactionId');
    });

    it('should include threeDS data with offer code when both are provided', async () => {
      mockGetUserInfo.mockResolvedValue({
        country: 'UK',
        type: UserType.PAYG_WALLET,
        status: '200',
        message: 'Success',
        partnerType: 'Uber',
        subsEnabled: true,
        membership: [],
      });
      mockGetUserSubsPlan.mockResolvedValue({
        status: '200',
        plans: [{ externalPlanId: planId, planDuration: 30 }],
        message: 'Success',
      });
      mockValidateOffer.mockResolvedValue({
        offer: { subsPlanId: '1235' },
        isValid: true,
      });

      const result = await createWalletSubscription(
        ctx,
        userId,
        paymentMethodId,
        offerCode,
        headers,
        mockThreeDS,
      );

      expect(result).toEqual({
        status: '200',
      });
      expect(mockedAxios.post).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          customerId: userId,
          paymentMethodId,
          threeDS: mockThreeDS,
        }),
        expect.any(Object),
      );
    });

    it('should handle partial threeDS data correctly', async () => {
      const partialThreeDS: ThreeDs = {
        eciFlag: '05',
        enrolled: 'Y',
        cavv: 'AAABBIIFmAAAAAAAAAAAAAAAAAA=',
        threeDSServerTransactionId: '12345678-1234-1234-1234-123456789012',
        paresStatus: 'Y',
        threeDSVersion: '2.1.0',
      };

      mockGetUserInfo.mockResolvedValue({
        country: 'UK',
        type: UserType.PAYG_WALLET,
        status: '200',
        message: 'Success',
        partnerType: 'Uber',
        subsEnabled: true,
        membership: [],
      });
      mockGetUserSubsPlan.mockResolvedValue({
        status: '200',
        plans: [{ externalPlanId: planId, planDuration: 30 }],
        message: 'Success',
      });

      const result = await createWalletSubscription(
        ctx,
        userId,
        paymentMethodId,
        undefined,
        headers,
        partialThreeDS,
      );

      expect(result).toEqual({
        status: '200',
      });
      expect(mockedAxios.post).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          customerId: userId,
          paymentMethodId,
          threeDS: partialThreeDS,
        }),
        expect.any(Object),
      );

      const callArgs = mockedAxios.post.mock.calls[0];
      const requestBody = callArgs[1];
      expect(requestBody).not.toHaveProperty('statusReason');
      expect(requestBody).not.toHaveProperty('dsTransactionId');
      expect(requestBody).not.toHaveProperty('acsTransactionId');
    });

    it('should handle threeDS data in createSubscriptionAsync for returning customers', async () => {
      ctx.logger.error = jest.fn();
      ctx.logger.info = jest.fn();
      mockGetUserInfo.mockResolvedValue({
        country: 'UK',
        type: UserType.PAYG_WALLET,
        status: '200',
        message: 'Success',
        subsEnabled: true,
        membership: [{ id: 'existing-membership' }],
      });
      mockGetUserSubsPlan.mockResolvedValue({
        status: '200',
        plans: [{ externalPlanId: planId, planDuration: 30 }],
        message: 'Success',
      });

      mockValidateOffer.mockResolvedValue({
        offer: { subsPlanId: '1235' },
        isValid: true,
      });

      mockedAxios.post.mockResolvedValue({
        data: { subscriptionId: 'mock-id' },
      });
      mockGetWalletSubscription.mockResolvedValue({
        id: 'mock-id',
        nextBillingDate: '2023-07-01',
      });

      await createWalletSubscription(
        ctx,
        userId,
        paymentMethodId,
        offerCode,
        headers,
        mockThreeDS,
      );

      await new Promise((resolve) => setImmediate(resolve));

      expect(mockedAxios.post).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          customerId: userId,
          paymentMethodId,
          threeDS: mockThreeDS,
        }),
        expect.any(Object),
      );
      expect(mockGetWalletSubscription).toHaveBeenCalled();
      expect(mockSetDefaultPaymentMethodWallet).toHaveBeenCalled();
      expect(mockCreateMembershipInternal).toHaveBeenCalled();
      expect(ctx.logger.info).toHaveBeenCalledWith(
        expect.stringContaining('Subscription created successfully'),
      );
    });

    it('should filter out empty string values from threeDS object', () => {
      const threeDSWithEmptyValues: ThreeDs = {
        eciFlag: '',
        enrolled: 'Y',
        cavv: 'AAABBIIFmAAAAAAAAAAAAAAAAAA=',
        threeDSServerTransactionId: '12345678-1234-1234-1234-123456789012',
        paresStatus: 'Y',
        threeDSVersion: '2.1.0',
        statusReason: '',
        dsTransactionId: '87654321-4321-4321-4321-210987654321',
        acsTransactionId: 'abcdef12-3456-7890-abcd-ef1234567890',
      };

      const result = constructThreeDSObject(threeDSWithEmptyValues);
      expect(result).toEqual({
        threeDS: {
          enrolled: 'Y',
          cavv: 'AAABBIIFmAAAAAAAAAAAAAAAAAA=',
          threeDSServerTransactionId: '12345678-1234-1234-1234-123456789012',
          paresStatus: 'Y',
          threeDSVersion: '2.1.0',
          dsTransactionId: '87654321-4321-4321-4321-210987654321',
          acsTransactionId: 'abcdef12-3456-7890-abcd-ef1234567890',
        },
      });
    });

    it('should filter out null values from threeDS object', () => {
      const threeDSWithNullValues: ThreeDs = {
        eciFlag: '05',
        enrolled: 'Y',
        cavv: 'AAABBIIFmAAAAAAAAAAAAAAAAAA=',
        threeDSServerTransactionId: '12345678-1234-1234-1234-123456789012',
        paresStatus: 'Y',
        threeDSVersion: '2.1.0',
        statusReason: null as any,
        dsTransactionId: null as any,
        acsTransactionId: 'abcdef12-3456-7890-abcd-ef1234567890',
      };

      const result = constructThreeDSObject(threeDSWithNullValues);
      expect(result).toEqual({
        threeDS: {
          eciFlag: '05',
          enrolled: 'Y',
          cavv: 'AAABBIIFmAAAAAAAAAAAAAAAAAA=',
          threeDSServerTransactionId: '12345678-1234-1234-1234-123456789012',
          paresStatus: 'Y',
          threeDSVersion: '2.1.0',
          acsTransactionId: 'abcdef12-3456-7890-abcd-ef1234567890',
        },
      });
    });

    it('should filter out undefined values from threeDS object', () => {
      const threeDSWithUndefinedValues: ThreeDs = {
        eciFlag: '05',
        enrolled: 'Y',
        cavv: 'AAABBIIFmAAAAAAAAAAAAAAAAAA=',
        threeDSServerTransactionId: '12345678-1234-1234-1234-123456789012',
        paresStatus: undefined as any,
        threeDSVersion: '2.1.0',
        statusReason: '01',
        dsTransactionId: undefined as any,
        acsTransactionId: 'abcdef12-3456-7890-abcd-ef1234567890',
      };

      const result = constructThreeDSObject(threeDSWithUndefinedValues);
      expect(result).toEqual({
        threeDS: {
          eciFlag: '05',
          enrolled: 'Y',
          cavv: 'AAABBIIFmAAAAAAAAAAAAAAAAAA=',
          threeDSServerTransactionId: '12345678-1234-1234-1234-123456789012',
          threeDSVersion: '2.1.0',
          statusReason: '01',
          acsTransactionId: 'abcdef12-3456-7890-abcd-ef1234567890',
        },
      });
    });

    it('should return empty threeDS object when all values are empty/null/undefined', () => {
      const emptyThreeDS: ThreeDs = {
        eciFlag: '',
        enrolled: null as any,
        cavv: undefined as any,
        threeDSServerTransactionId: '',
        paresStatus: null as any,
        threeDSVersion: undefined as any,
        statusReason: '',
        dsTransactionId: null as any,
        acsTransactionId: undefined as any,
      };

      const result = constructThreeDSObject(emptyThreeDS);
      expect(result).toEqual({
        threeDS: {},
      });
    });

    it('should handle empty threeDS object input', () => {
      const result = constructThreeDSObject({} as ThreeDs);
      expect(result).toEqual({
        threeDS: {},
      });
    });
  });
});
